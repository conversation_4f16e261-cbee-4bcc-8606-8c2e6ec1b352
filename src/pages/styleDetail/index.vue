<template>
    <view class="page style-detail">
        <view class="style-detail__header">
            <view class="top-row">
                <view class="info-item">
                    <text class="title">款式名：</text>
                    <text class="value">{{ styleInfo.styleName && styleInfo.styleName.slice(0, 10) || 'sdasdjkadalsd' }}</text>
                </view>
                <view class="info-item">
                    <text class="title">总面积：</text>
                    <text class="value">{{ (styleInfo.totalArea || 0).toFixed(2) }}ft²</text>
                </view>
            </view>
            <view class="level-row top-row">
                <view class="info-item">
                    <text class="title">A：</text>
                    <text class="value">{{ styleInfo.levelA || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">B：</text>
                    <text class="value">{{ styleInfo.levelB || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">C：</text>
                    <text class="value">{{ styleInfo.levelC || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">D：</text>
                    <text class="value">{{ styleInfo.levelD || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">E：</text>
                    <text class="value">{{ styleInfo.levelE || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">其他：</text>
                    <text class="value">{{ styleInfo.otherLevel || 0 }}</text>
                </view>
                <uni-icons class="style-select"
                    type="list"
                    color="#fff"
                    size="20"
                    @click.prevent="switchStyleVisible(true)"
                ></uni-icons>
            </view>
            <view class="style-list" v-show="showStyleListPopup" @click.stop>
                <CpList
                    :activeId="id"
                    :is-in-popup="true"
                    :orderId="orderId"
                    :tableData="styleList"
                    @rowClick="handleChangeStyle"
                >
                </CpList>
            </view>
        </view>
        <view class="style-detail__content" @click="switchStyleVisible(false)">
            <view class="common-table">
                <view class="common-table__header">
                    <view class="th-item order">编号</view>
                    <view class="th-item flex">{{ isReadonly ? '已选择裁片' : '裁片名' }}</view>
                    <view class="th-item quantity">数量</view>
                    <view class="th-item roate">旋转</view>
                    <view class="th-item angle">倾斜</view>
                    <view class="th-item line">优先级</view>
                    <view class="th-item limit">上限</view>
                </view>
                <view class="common-table__body">
                    <view class="row-item"
                        v-for="(item, index) in cpOrderBlock"
                        :key="index"
                        @click="handleEditItem(index)"
                        :class="{'light-bg': index%2}"
                        :style="isReadonly ? 'pointer-events: none;' : ''"
                    >
                        <view class="row-item__column order">{{ index + 1 }}</view>
                        <view class="row-item__column flex">
                            <view class="info-name">{{ item.name || '' }}</view>
                            <view class="info-detail area-info">
                                <text>{{ item.area || 0 }} ft²</text>
                            </view>
                        </view>
                        <view class="row-item__column quantity">{{ item.quantity || 1 }}</view>
                        <view class="row-item__column roate">{{ item.rotationRestriction || '-' }}</view>
                        <view class="row-item__column angle">{{ item.tiltAngle || '-' }}</view>
                        <view class="row-item__column line">{{ item.blPriority || '-' }}</view>
                        <view class="row-item__column limit">{{ item.singlePlMax || '-' }}</view>
                    </view>
                    <view class="no-data" v-if="!cpOrderBlock.length">
                        暂无数据
                    </view>
                </view>
            </view>
        </view>
        <view class="style-detail__footer" @click="switchStyleVisible(false)">
            <div class="form-row">
                <span class="label">每块裁片套数：</span>
                <template v-if="!isReadonly">
                    <div class="input-part">
                        <span class="opt-icon" @click="changeQuantity(true)">-</span>
                        <input class="number-input"
                            type="number"
                            min="1"
                            :adjust-position="false"
                            v-model="cpQuantityMulti"
                            placeholder="请输入"
                            @change="changeCpQuantityMulti"
                            :disabled="isReadonly"
                        />
                        <span class="opt-icon" @click="changeQuantity(false)">+</span>
                    </div>
                    <div class="sub-btn" @click="handleMulti">确认</div>
                    <img @click="resetQuantity" class="reset-icon 1" width="20" height="20" src="/static/reset.svg" />
                </template>
                <span v-else>{{ cpQuantityMulti }}</span>
            </div>
            <div class="form-row">
                <span class="label">排料优先级：</span>
                <template v-if="!isReadonly">
                    <uni-data-checkbox class="input-part"
                        v-model="styleInfo.cpPriority"
                        :localdata="cpPriorityOptions"
                        @change="resetDisabled"
                        :disabled="isReadonly"
                    ></uni-data-checkbox>
                </template>
                <span v-else>{{ styleInfo.cpPriority }}</span>
            </div>
            <div class="form-row multi-row">
                <view class="half left">
                    <span class="label">排到空皮料：</span>
                    <template v-if="!isReadonly">
                        <checkbox class="checkbox"
                            :checked="styleInfo.setCompletelyEmptyPl"
                            :value="true"
                            color="#127FD2"
                            backgroundColor="#000"
                            @click="handleSelectChange('setCompletelyEmptyPl')"
                            :disabled="isReadonly"
                        ></checkbox>
                    </template>
                    <span v-else class="readonly-text">{{ styleInfo.setCompletelyEmptyPl ? '是' : '否' }}</span>
                </view>
                <view class="half right">
                    <span class="label">若不成功则失败：</span>
                    <template v-if="!isReadonly">
                        <checkbox
                            class="checkbox"
                            color="#127FD2"
                            :checked="styleInfo.failOnFailure"
                            @click="handleSelectChange('failOnFailure')"
                            :value="true"
                            :disabled="isReadonly"
                        ></checkbox>
                    </template>
                    <span v-else class="readonly-text">{{ styleInfo.failOnFailure ? '是' : '否' }}</span>
                </view>
            </div>
            <div class="form-row multi-row">
                <view class="half left">
                    <span class="label">排到可重用废料上：</span>
                    <template v-if="!isReadonly">
                        <checkbox class="checkbox"
                            :checked="styleInfo.setReusableFl"
                            :value="true"
                            color="#127FD2"
                            @click="handleSelectChange('setReusableFl')"
                            :disabled="isReadonly"
                        ></checkbox>
                    </template>
                    <span v-else class="readonly-text">{{ styleInfo.setReusableFl ? '是' : '否' }}</span>
                </view>
                <view class="half right">
                    <span class="label">参与利用率计算：</span>
                    <template v-if="!isReadonly">
                        <checkbox
                            class="checkbox"
                            color="#127FD2"
                            :checked="styleInfo.forUseRate"
                            @click="handleSelectChange('forUseRate')"
                            :value="true"
                            :disabled="isReadonly"
                        ></checkbox>
                    </template>
                    <span v-else class="readonly-text">{{ styleInfo.forUseRate ? '是' : '否' }}</span>
                </view>
            </div>

            <div class="form-row multi-row">
                <view class="half left">
                    <span class="label">不计入总裁片数量：</span>
                    <template v-if="!isReadonly">
                        <checkbox class="checkbox"
                            :checked="styleInfo.noTotalQuantityCount"
                            @click="handleSelectChange('noTotalQuantityCount')"
                            :value="true"
                            color="#127FD2"
                            :disabled="isReadonly"
                        ></checkbox>
                    </template>
                    <span v-else class="readonly-text">{{ styleInfo.noTotalQuantityCount ? '是' : '否' }}</span>
                </view>
                <view class="half right btn-part">
                    <button
                        v-if="!isReadonly"
                        class="save-btn btn-primary"
                        :disabled="submitDiabled"
                        :class="submitDiabled ? 'disabled': ''"
                        @click="handleSave"
                    >保 存</button>
                </view>
            </div>
        </view>
        <uni-popup ref="editCpPopup" type="bottom" background-color="#000" @change="editPopupChange">
            <view class="edit-popup-content">
                <uni-icons class="close-popup"
                    type="close"
                    color="#fff"
                    size="26"
                    @click="closeEditPopup"
                ></uni-icons>
                <view class="left-part">
                    <view class="config-row">
                        <text class="label">裁片数量：</text>
                        <div class="number-input input-part">
                            <span class="opt-icon" @click="decreaseNum('quantity')">-</span>
                            <input class="number-input"
                                type="number"
                                :adjust-position="false"
                                placeholder="请输入"
                                @change="changeValue('quantity')"
                                v-model="cpOrderBlock[currentEditIndex].quantity"
                                :disabled="isReadonly"
                            />
                            <!--                            <span class="number-input">{{ cpOrderBlock[currentEditIndex].quantity || 0 }}</span>-->
                            <span class="opt-icon" @click="increaseNum('quantity')">+</span>
                        </div>
                    </view>
                    <view class="config-row">
                        <text class="label">旋转限制：</text>
                        <uni-data-select
                            class="input-part"
                            v-model="cpOrderBlock[currentEditIndex].rotationRestriction"
                            :localdata="roateSelectOptions"
                            style="background: #000;"
                            @change="changeRoateSelect"
                            :disabled="isReadonly"
                        ></uni-data-select>
                    </view>
                    <view class="config-row">
                        <text class="label">倾斜偏移：</text>
                        <div class="number-input input-part">
                            <span class="opt-icon" @click="decreaseNum('tiltAngle')">-</span>
                            <input class="number-input"
                                type="number"
                                placeholder="请输入"
                                :adjust-position="false"
                                @change="changeValue('tiltAngle')"
                                v-model="cpOrderBlock[currentEditIndex].tiltAngle"
                                :disabled="isReadonly"
                            />
                            <!--                            <input class="number-input" type="numeric" v-model="cpOrderBlock[currentEditIndex].tiltAngle" />-->
                            <span class="opt-icon" @click="increaseNum('tiltAngle')">+</span>
                        </div>
                    </view>
                    <view class="config-row">
                        <text class="label">排版优先：</text>

                        <uni-data-select
                            class="input-part"
                            v-model="cpOrderBlock[currentEditIndex].blPriority"
                            :localdata="cpPriorityOptions"
                            style="background: #000;"
                            @change="changeRoateSelect"
                            :disabled="isReadonly"
                        ></uni-data-select>
                        <!--                        <div class="number-input input-part">-->
                        <!--                            <span class="opt-icon" @click="decreaseNum('blPriority')">-</span>-->
                        <!--                            &lt;!&ndash;                            <span class="number-input">{{ cpOrderBlock[currentEditIndex].blPriority || 0 }}</span>&ndash;&gt;-->
                        <!--                            <input class="number-input"-->
                        <!--                                type="number"-->
                        <!--                                placeholder="请输入"-->
                        <!--                                v-model="cpOrderBlock[currentEditIndex].blPriority"-->
                        <!--                            />-->
                        <!--                            <span class="opt-icon" @click="increaseNum('blPriority')">+</span>-->
                        <!--                        </div>-->
                    </view>
                    <view class="config-row">
                        <text class="label">单皮上限：</text>
                        <div class="number-input input-part">
                            <span class="opt-icon" @click="decreaseNum('singlePlMax')">-</span>
                            <!--                            <span class="number-input">{{ cpOrderBlock[currentEditIndex].singlePlMax || 0 }}</span>-->
                            <input class="number-input"
                                type="number"
                                placeholder="请输入"
                                :adjust-position="false"
                                @change="changeValue('singlePlMax')"
                                v-model="cpOrderBlock[currentEditIndex].singlePlMax"
                                :disabled="isReadonly"
                            />
                            <span class="opt-icon" @click="increaseNum('singlePlMax')">+</span>
                        </div>
                    </view>
                </view>
                <view class="right-part" v-if="cpOrderBlock[currentEditIndex].svgPath">
                    <image
                        :src="cpOrderBlock[currentEditIndex].svgPath"
                        class="preview-img"
                    />
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import CpList from 'components/cpList/index.vue';
import { getCpList, getOrderCpOriginDetail, getOrderStyleList, saveStyleConfig } from 'api/order';

export default {
    components: {
        CpList,
    },
    data() {
        return {
            orderId: '',
            id: '',
            loading: false,
            submitDiabled: false,
            styleInfo: {},
            cpOrderBlock: [],
            currentEditIndex: '',
            styleList: [],
            roateSelectOptions: [
                {
                    value: '自由',
                    text: '自由',
                }, {
                    value: '固定',
                    text: '固定',
                }, {
                    value: '180°',
                    text: '180°',
                }, {
                    value: '90°',
                    text: '90°',
                }, {
                    value: '45°',
                    text: '45°',
                }, {
                    value: '布纹线',
                    text: '布纹线',
                },
            ],
            showStyleListPopup: false,
            numVal: 0,
            baseUrl: this.$http.baseURL,
            cpQuantityMulti: 1,
            cpPriorityOptions: [{
                text: '1',
                value: 1,
            }, {
                text: '2',
                value: 2,
            }, {
                text: '3',
                value: 3,
            }, {
                text: '4',
                value: 4,
            }],
            svgBase64: '',
            isReadonly: false,
        };
    },
    watch: {
        cpOrderBlock: {
            handler() {
                this.resetDisabled();
            },
            deep: true,
        },
    },
    async onLoad(query) {
        this.orderId = query.orderId;
        this.id = query.id;
        this.isReadonly = query.readonly === 'true';
    },
    async onShow() {
        if (!this.orderId || !this.id) {
            return;
        }
        this.cpQuantityMulti = 1;
        await this.getStyleInfo();
        await this.initCpList();
    },
    methods: {
        renderImgSvg(url) {
            console.log('url = ', url);
            const that = this;
            wx.request({
                url, // 你的接口地址
                method: 'GET', // 请求方式，默认为GET
                header: {
                    'content-type': 'application/json', // 默认值
                },
                success: function(res) {
                    // 请求成功的回调函数
                    const base64 = wx.arrayBufferToBase64(
                        new Uint8Array(unescape(encodeURIComponent(res.data)).split('').map(c => c.charCodeAt(0))),
                    );
                    const dataURL = `data:image/svg+xml;base64,${base64}`;
                    that.svgBase64 = dataURL;
                    // that.svgUrl = res.data.replace(/<svg/, `<svg width="${80}" height="${80}"`);
                },
                fail: function(err) {
                    // 请求失败的回调函数
                    console.error('数据获取失败:', err);
                },
            });
        },
        resetDisabled() {
            this.submitDiabled = false;
        },
        changeCpQuantityMulti() {
            this.submitDiabled = false;
            if (!this.cpQuantityMulti || this.cpQuantityMulti < 1) {
                this.$toast.error('最小值为1');
                this.cpQuantityMulti = 1;
                return;
            }
            this.$nextTick(() => {
                this.cpQuantityMulti = +this.cpQuantityMulti;
            });
        },
        handleSelectChange(key) {
            if (this.isReadonly) {
                return;
            }
            this.resetDisabled();
            this.styleInfo[key] = !this.styleInfo[key];
        },
        handleMulti() {
            this.cpOrderBlock.forEach(item => {
                item.quantity = item.quantity * this.cpQuantityMulti;
            });
        },
        resetQuantity() {
            getOrderCpOriginDetail(this.orderId, this.styleInfo.cpId).then((res) => {
                const { code, data } = res.data;
                if (code === 0) {
                    this.cpQuantityMulti = 1;
                    this.cpOrderBlock.forEach((item) => {
                        const originQuantity = data.find(a => a.id === item.id).quantity;
                        item.quantity = originQuantity;
                    });
                }
            });
        },
        changeQuantity(isDecrease) {
            if (!this.cpQuantityMulti) {
                this.cpQuantityMulti = 1;
            }
            if (isDecrease) {
                this.cpQuantityMulti > 1 && this.cpQuantityMulti--;
            } else {
                this.cpQuantityMulti = this.cpQuantityMulti + 1;
            }
            this.resetDisabled();
        },
        decreaseNum(key) {
            if (this.isReadonly) {
                return;
            }
            if (!this.cpOrderBlock[this.currentEditIndex] || +this.cpOrderBlock[this.currentEditIndex][key] === 0) {
                return;
            }
            this.cpOrderBlock[this.currentEditIndex][key]--;
            this.resetDisabled();
        },
        increaseNum(key) {
            if (this.isReadonly) {
                return;
            }
            this.cpOrderBlock[this.currentEditIndex][key]++;
            this.resetDisabled();
        },
        changeValue(key) {
            if (+this.cpOrderBlock[this.currentEditIndex][key] < 1) {
                this.$toast.error('最小值为1');
                this.cpOrderBlock[this.currentEditIndex][key] = 1;
                return 1;
            }
            this.$nextTick(() => {
                this.cpOrderBlock[this.currentEditIndex][key] = +this.cpOrderBlock[this.currentEditIndex][key];
                this.$forceUpdate();
            });
            this.resetDisabled();
        },
        changeRoateSelect(e) {
            console.log('changeRoateSelect = ', e);
        },
        closeEditPopup() {
            this.svgBase64 = '';
            this.$refs.editCpPopup.close();
        },
        editPopupChange(e) {
            if (!e.show) {
                this.currentEditIndex = '';
                this.svgBase64 = '';
            }
        },
        handleEditItem(index) {
            if (this.isReadonly) {
                return;
            }
            this.showStyleListPopup = false;
            this.currentEditIndex = index;
            this.$refs.editCpPopup.open();
        },
        handleChangeStyle(id) {
            if (id === this.id) {
                this.showStyleListPopup = false;
                return;
            }
            this.id = id;
            this.getStyleInfo();
        },
        switchStyleVisible(v) {
            if (this.showStyleListPopup) {
                this.showStyleListPopup = false;
                return;
            }
            this.showStyleListPopup = v;
        },
        initCpList() {
            getCpList({
                orderId: this.orderId,
                size: 1000,
                current: 1,
            }).then(res => {
                const { code, data } = res.data;
                if (code === 0) {
                    this.styleList = data;
                }
            });
        },
        getStyleInfo() {
            this.loading = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            getOrderStyleList(this.id).then((res) => {
                const { code, data } = res.data;
                if (code === 0) {
                    this.styleInfo = data;
                    this.styleInfo.cpQuantity = this.styleInfo.cpQuantity || 1;
                    this.cpOrderBlock = data.cpOrderBlock;
                }
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
        handleSave() {
            this.submitDiabled = true;
            const params = {
                ...this.styleInfo,
                cpOrderBlock: [...this.cpOrderBlock],
            };
            saveStyleConfig(params).then(res => {
                const { code } = res.data;
                if (code === 0) {
                    this.$toast.success('保存成功');
                    uni.navigateBack({
                        delta: 1,
                    });
                } else {
                    this.submitDiabled = false;
                    this.$toast.error('保存失败');
                }
            }).catch(() => {
                this.submitDiabled = false;
                this.$toast.error('保存失败');
            });
        },
    },
};
</script>

<style lang="scss">
.style-detail {
    height: 100vh;
    background: #fff;
    &__header {
        position: relative;
        background: #000;
        color: #fff;
        .top-row {
            height: 60rpx;
            padding: 0 30rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {}
            .value {
                color: #A7A7A7;
            }
            &.level-row {
                border-top: 1px solid #A7A7A7;
            }
        }
        .style-list {
            position: absolute;
            right: 6rpx;
            top: 125rpx;
            width: 99vw;
            color: #000;
            border: 1px solid #000;
            background: #fff;
            box-shadow: 0px 4px 10px 0px #000000;
            .common-table {
                padding-bottom: 0;
            }
        }
    }
    &__content {
        height: calc(100vh - 600rpx);
        .common-table {
            .area-info {
                display: flex;
                justify-content: space-between;
                font-weight: bold;
            }
            .row-item__column {
                font-size: 24rpx;
            }
            .quantity {
                width: 70rpx;
            }
            .roate,.angle, .limit {
                width: 80rpx;
            }
            .line {
                width: 93rpx;
            }
        }
    }
    &__footer {
        background: #000;
        color: #fff;
        height: 430rpx;
        width: 100vw;
        padding: 10rpx 0;
        position: absolute;
        bottom: 0;
        .form-row {
            display: flex;
            align-items: center;
            height: 80rpx;
            padding: 0 24rpx;
            font-size: 28rpx;
            .label {
                width: 245rpx;
            }
            .input-part {
                display: flex;
                justify-content: space-between;
                .opt-icon {
                    border: 2px solid #fff;
                    border-radius: 6rpx;
                    display: inline-block;
                    width: 36rpx;
                    height: 36rpx;
                    text-align: center;
                    line-height: 32rpx;
                    font-size: 36rpx;
                }
                .checklist-text {
                    color: #fff !important;
                    font-size: 32rpx;
                }
            }
            .number-input {
                width: 140rpx;
                text-align: center;
                margin: 0 10rpx;
            }
            .sub-btn {
                width: 90rpx;
                height: 46rpx;
                text-align: center;
                margin-left: 30rpx;
                background: #127FD2;
                line-height: 46rpx;
                border-radius: 2px;
            }
            .reset-icon {
                margin-left: 20rpx;
                width: 40rpx;
                height: 40rpx;
            }

            .half {
                width: 50%;
                display: flex;
                align-items: center;
                .save-btn {
                    width: 180rpx;
                    height: 60rpx;
                    background: #FF0000;
                    line-height: 60rpx;
                    font-size: 32rpx;
                    font-weight: 700;
                    color: #fff !important;
                    float: right;
                    margin: 0;
                    margin-right: 14rpx;
                    &.disabled {
                        background: #cccccc;
                        pointer-events: none;
                        color: #999;
                    }
                }
                .readonly-text {
                    // color: #fff;
                    // font-size: 24rpx;
                    // margin-left: 10rpx;
                }
                &.left {
                    justify-content: flex-start;
                }
                &.right {
                    justify-content: flex-end;
                }
            }
        }
    }
    .edit-popup-content {
        color: #fff;
        display: flex;
        justify-content: space-between;
        .close-popup {
            position: absolute;
            right: 20rpx;
            top: -60rpx;
        }
        .left-part {
            flex: 1;
            padding: 30rpx;
            margin-bottom: 20rpx;
            .config-row {
                display: flex;
                height: 80rpx;
                align-items: center;
                justify-content: space-between;
                .label {
                    font-size: 30rpx;
                    width: 150rpx;
                }
                .input-part {
                    display: flex;
                    justify-content: space-between;
                    padding: 0 40rpx;
                    width: 210rpx;
                    .opt-icon {
                        border: 2px solid #fff;
                        border-radius: 6rpx;
                        display: inline-block;
                        width: 36rpx;
                        height: 36rpx;
                        text-align: center;
                        line-height: 32rpx;
                        font-size: 36rpx;
                    }
                    .number-input {
                        text-align: center;
                        //pointer-events: none;
                        width: 100rpx;
                    }
                    .uni-select {
                        &__input-text {
                            color: #fff;
                        }
                        &__selector {
                            padding: 0;
                            background: #000;
                            width: 243rpx;
                            left: -2px;
                            top: 40px !important;
                            .uni-popper__arrow_bottom {
                                top: -7px;
                            }
                            &-item {
                                height: 40rpx;
                                line-height: 40rpx;
                                font-size: 24rpx;
                                &:hover {
                                    background: #3F536E;
                                }
                            }
                        }
                    }

                }
                ::v-deep .uni-select__selector, ::v-deep .uni-stat-box {
                    background: #000;
                    .uni-select__input-text {
                        color: #fff;
                    }
                }
            }
        }
        .right-part {
            width: 320rpx;
            padding: 20rpx;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            .preview-img {
                width: 100%;
                height: 260rpx;
                background: #fff;
            }
        }
    }
}
</style>
