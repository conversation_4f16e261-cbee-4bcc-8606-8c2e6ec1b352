<template>
    <view class="page page-config">
        <view class="page-config__tab">
            <view class="tab-item"
                :class="currentTabIndex === index ? 'active': ''"
                v-for="(item, index) in tabItems"
                @click="onClickItem(index)"
                :key="item"
            >{{ item }}</view>
        </view>
        <view class="page-config__content" v-show="!loading" :class="currentTabIndex === 0 ? 'more-height' : ''">
            <view class="order-base" v-show="currentTabIndex > 0">
                <view class="info-item">任务名：<text class="info-num">{{ orderInfo.orderName }}</text></view>
                <view class="detail-info">
                    <text class="info-item">皮料数：<text class="info-num">{{ orderInfo.plQuantity || 0 }}</text></text>
                    <text class="info-item">裁片数：<text class="info-num">{{ orderInfo.cpQuantity || 0 }}</text></text>
                    <text class="info-item">排版时间：<text class="info-num">{{ orderInfo.mostTime || 0 }}秒</text></text>
                    <uni-icons class="order-other"
                        slot="default"
                        @click="handleShowOrderOther"
                        :type="showPopover ? 'up' : 'down'"
                        color="#fff"
                        size="20"
                    ></uni-icons>
                    <view class="order-other-popover" v-show="showPopover">
                        <p class="info-row">优先排版：
                            <view class="info-text">{{ orderInfo.priority || '无' }}</view>
                        </p>
                        <p class="info-row">备注 ：
                            <view class="info-text">{{ orderInfo.remark || '无' }}</view>
                        </p>
                    </view>
                </view>
            </view>
            <AddOrder v-show="currentTabIndex === 0"
                :visible="currentTabIndex === 0"
                :orderId="orderId"
                :initData="orderInfo"
                :readonly="isReadonly"
                @updateOrderId="handleUpdateOrderId"
                @updateInfo="updateOrderInfo"
            ></AddOrder>
            <CpList
                :tableData="cpTableData"
                :orderId="orderId"
                :readonly="isReadonly"
                v-show="currentTabIndex === 1"
            ></CpList>
            <LeatherList
                :tableData="leatherTableData"
                :orderId="orderId"
                v-show="currentTabIndex === 2"
                @delete="handleDelete"
                @updateDataList="updateDataList"
            ></LeatherList>
        </view>
        <view class="page-config__footer" v-if="currentTabIndex > 0 && !loading">
            <view class="float-fab" @click="handleToAdd">
                <uni-icons class="fab-icon" type="plusempty" color="#fff" size="40"></uni-icons>
            </view>
            <view class="area-info" :class="showAreaDetail ? 'active' : ''">
                <template v-if="showAreaDetail">
                    <view class="area-info__title">
                        <text>任务所需皮料面积(ft²)</text>
                        <uni-icons @click="handleShowAreaDetail" class="info-icon" type="close" color="#fff" size="30"></uni-icons>
                    </view>
                    <view class="area-table">
                        <view class="area-table__thead">
                            <view>皮料瑕疵等级</view>
                            <view>需要面积</view>
                            <view>已选皮料面积</view>
                            <view>差异</view>
                        </view>
                        <view class="area-table__tbody">
                            <view class="row-item" v-for="(item, index) in areaTableData" :key="index">
                                <view>{{ item.level }}</view>
                                <view>{{ item.cpArea }}</view>
                                <view>{{ item.plArea }}</view>
                                <view :class="item.plArea - item.cpArea >= 0 ? 'green' : 'red'">{{ item.plArea -
                                    item.cpArea
                                }}</view>
                            </view>
                        </view>
                    </view>
                </template>
            </view>
            <view class="bottom-part">
                <view class="bottom-part__left">
                    <view class="top">所需皮料面积 (ft²)
                        <view class="show-area inline-block" @click="handleShowAreaDetail">
                            查看详情
                            <uni-icons class="fab-icon"
                                :type="showAreaDetail ? 'up' : 'down'"
                                color="#02A7F0"
                                size="16"
                            ></uni-icons>
                        </view>
                    </view>
                    <view class="level">
                        <text :class="area.plArea - area.cpArea >= 0 ? 'green' : 'red'"
                            v-for="(area, index) in orderInfo.plOrderLevelAreas"
                            :key="index"
                        >
                            {{ area.level }}: {{ Number(area.plArea - area.cpArea).toFixed(3) }}
                        </text>
                    </view>
                </view>
                <view class="bottom-part__right">
                    <button type="primary"
                        :disabled="broadBtnDisabled"
                        :class="broadBtnDisabled ? 'disabled': ''"
                        @click="handleBroadcastOrder"
                    >发布任务</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import CpList from 'components/cpList/index.vue';
import AddOrder from 'components/addOrder/index.vue';
import LeatherList from 'components/leatherList/index.vue';
import { getCpList, getLetherList, getOrderBaseInfo, releaseOrder } from 'api/order';

export default {
    components: {
        CpList,
        AddOrder,
        LeatherList,
    },
    data() {
        return {
            currentTabIndex: '',
            tabItems: ['任务', '裁片', '皮料'],
            orderId: '',
            leatherTableData: [],
            cpTableData: [],
            loading: false,
            orderInfo: {},
            showAreaDetail: false,
            showPopover: false,
            areaTableData: [],
            broadBtnDisabled: false,
            isReadonly: false,
        };
    },
    onLoad(query) {
        if (query.orderId) {
            this.orderId = query.orderId;
        }
        this.currentTabIndex = query.activeTab ? +query.activeTab : 0;
        this.isReadonly = query.readonly === 'true';
    },
    async onShow() {
        this.showPopover = false;
        this.showAreaDetail = false;
        this.broadBtnDisabled = false;

        this.orderId && await this.getOrderInfo();
    },
    methods: {
        handleDelete(index) {
            this.leatherTableData.splice(index, 1);
        },
        updateDataList(data) {
            this.leatherTableData = [...data];
        },
        handleToAdd() {
            const toPage = this.currentTabIndex === 1 ? 'pages/addStyle/index' : 'pages/addLeather/index';
            uni.navigateTo({
                url: `${toPage}?orderId=${this.orderId}`,
            });
        },
        handleBroadcastOrder() {
            if (this.broadBtnDisabled) {
                return;
            }
            if (!this.cpTableData.length) {
                this.$toast.error('请先添加裁片');
                return;
            } else if (!this.leatherTableData.length) {
                this.$toast.error('请先添加皮料');
                return;
            } else if (this.orderInfo.plOrderLevelAreas.some(a => a.plArea - a.cpArea < 0)) {
                return this.$toast.error('所需皮料面积不足');
            }
            this.broadBtnDisabled = true;

            uni.showLoading({
                title: '发布中...',
                mask: false,
            });
            releaseOrder({
                id: this.orderId,
                action: 1,
            }).then((res) => {
                const { code } = res.data;
                if (code === 0) {
                    this.$toast.success('发布成功');
                    uni.switchTab({
                        url: '/pages/plList/index',
                    });
                } else {
                    this.broadBtnDisabled = false;
                    this.$toast.error('发布失败');
                }
            }).catch(() => {
                this.broadBtnDisabled = false;
                this.$toast.error('发布失败');
            }).finally(() => {
                uni.hideLoading();
            });
        },
        handleShowAreaDetail() {
            this.showAreaDetail = !this.showAreaDetail;
        },
        handleShowOrderOther() {
            this.showPopover = !this.showPopover;
        },
        handleUpdateOrderId(orderId) {
            this.orderId = orderId;
        },
        updateOrderInfo() {
            this.getOrderInfo(true);
        },
        onClickItem(index) {
            if (!this.orderId) {
                this.$toast.error('请先进行任务保存！');
                return;
            }
            this.currentTabIndex = index;
        },
        getOrderInfo(hideLoading) {
            if (!hideLoading) {
                this.loading = true;
                uni.showLoading({
                    title: '加载中...',
                    mask: false,
                });
            }
            const params = {
                orderId: this.orderId,
                current: 1,
                size: 1000,
            };
            Promise.all([getOrderBaseInfo(this.orderId), getCpList(params), getLetherList(params)])
                .then((res) => {
                    const [baseInfo, cpData, letherData] = res;
                    if (baseInfo.data.code === 0) {
                        const { data } = baseInfo.data;
                        this.orderInfo = data;
                        this.areaTableData = [...data.plOrderLevelAreas];
                        this.areaTableData.push({
                            level: '总共',
                            plArea: data.plOrderLevelAreas.reduce((total, item) => {
                                return total + item.plArea;
                            }, 0),
                            cpArea: data.plOrderLevelAreas.reduce((total, item) => {
                                return total + item.cpArea;
                            }, 0),
                        });
                    }
                    if (cpData.data.code === 0) {
                        this.cpTableData = cpData.data.data;
                    }
                    if (letherData.data.code === 0) {
                        this.leatherTableData = letherData.data.data;
                    }
                })
                .finally(() => {
                    if (!hideLoading) {
                        setTimeout(() => {
                            uni.hideLoading();
                            this.loading = false;
                        }, 300);
                    }
                });
        },
    },
};
</script>

<style lang="scss">
.page-config {
    height: 100vh;
    background: #fff;
    overflow: hidden;
    &__tab {
        height: 100rpx;
        display: flex;
        align-items: center;
        background: #000;
        .tab-item {
            width: 33.33%;
            height: 100%;
            line-height: 100rpx;
            text-align: center;
            color: #999;
            &.active {
                color: #fff;
                font-weight: bold;
                border-bottom: 4px solid #FFF;
            }
        }
    }
    &__content {
        height: calc(100vh - 264rpx);
        &.more-height {
            height: calc(100vh - 104rpx);
        }
        .order-base {
            height: 96rpx;
            background: #000;
            padding: 16rpx;
            font-size: 32rpx;
            color: #fff;
            font-weight: bold;
            .info-item {
                padding-right: 16rpx;
            }
            .info-num {
                color: #A7A7A7;
                font-weight: normal;
            }
            .order-other {
                display: inline-block;
                float: right;
                margin-right: 20rpx;
            }
            .detail-info {
                position: relative;
                .order-other-popover {
                    position: absolute;
                    right: 0rpx;
                    background: #000;
                    top: 54rpx;
                    padding: 10rpx;
                    border-radius: 6rpx;
                    .info-row {
                        .info-text {
                            color: #A7A7A7;
                            font-weight: normal;
                            max-width: 400rpx;
                            word-break: break-all;
                            vertical-align: top;
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }
    &__footer {
        position: fixed;
        bottom: 0;
        background-color: #000;
        color: #fff;
        width: 100vw;
        .float-fab {
            position: absolute;
            bottom: 180rpx;
            left: 20rpx;
            background: #000;
            padding: 0 4rpx;
            border-radius: 8rpx;
            z-index: 1;
        }
        .area-info {
            transition: all 0.5s linear ;
            height: 0;
            width: 100vw;
            z-index: 9;
            position: absolute;
            bottom: 164rpx;
            background: #000;
            border-bottom: 1px solid #ededed;
            opacity: 0;
            font-weight: bold;
            &.active {
                height: 560rpx;
                opacity: 1;
            }
            &__title {
                height: 80rpx;
                line-height: 80rpx;
                padding: 0 30rpx 10rpx;
                font-size: 34rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .info-icon {
                    padding-top: 20rpx;
                }
            }
            .area-table {
                font-size: 28rpx;
                padding: 0 20rpx;
                &__thead {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    height: 80rpx;
                    view {
                        width: 25%;
                        text-align: center;
                    }
                }
                &__tbody {
                    .row-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 50rpx;
                        border-top: 1px solid #fff;
                        view {
                            width: 25%;
                            text-align: center;
                        }
                    }
                }
            }
        }
        .bottom-part {
            height: 164rpx;
            display: flex;
            width: 100vw;
            justify-content: space-between;
            z-index: 99;
            &__left {
                flex: 1;
                padding: 10rpx 0rpx 10rpx 20rpx;
                line-height: 46rpx;
                font-weight: bold;
                .top {
                    font-size: 28rpx;
                    .show-area {
                        padding-left: 30rpx;
                        color: #02A7F0;
                    }
                }
                .level {
                    font-size: 26rpx;
                    text {
                        padding-right: 6rpx;
                        display: inline-block;
                        min-width: 60rpx;
                    }
                }
            }
            &__right {
                width: 200rpx;
                padding: 0 20rpx;
                button {
                    width: 100%;
                    height: 70rpx;
                    font-size: 32rpx;
                    font-weight: 700;
                    line-height: 70rpx;
                    background: #0256FF;
                    &.disabled {
                        color: #999;
                        background: #CCCCCC;
                        pointer-events: none;
                    }
                }
            }
        }
    }

    .green {
        color:#00B52A;
    }
    .red {
        color:#FF0000;
    }
}
</style>
