<template>
    <view class="page pl-detail">
        <view class="pl-detail__header" @click="hidePopupList">
            <view class="row-item">
                <view class="info-item">
                    <text class="title">任务名：</text>
                    <text class="info">{{ orderInfo.orderName }}</text>
                </view>
                <view class="info-item">
                    <text class="title">排版总时间：</text>
                    <text class="info">{{ orderInfo.totalTypesettingTime }}分</text>
                </view>
            </view>
            <view class="row-item">
                <view class="info-item">
                    <text class="title">皮料数：</text>
                    <text class="info">{{ orderInfo.plQuantity || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">裁片数：</text>
                    <text class="info">{{ orderInfo.cpQuantity || 0 }}</text>
                </view>
                <view class="info-item">
                    <text class="title">剩余裁片：</text>
                    <text class="info">{{ orderInfo.remainingCpCount || 0 }}</text>
                </view>
            </view>

        </view>
        <view class="pl-detail__content">
            <template v-if="plOrderPlResultResponses.length">
                <view class="order-selector">
                    <text class="id">皮料ID {{ currentCpItem.plId || '' }}</text>
                    <text class="cut">裁片：{{ currentCpItem.cpQuantity || 0 }}</text>
                    <uni-icons class="pl-select"
                        type="list"
                        color="#000"
                        size="22"
                        @click.stop="switchPopupListVisible"
                    ></uni-icons>
                </view>
                <view class="popup-list" v-show="showPopupList">
                    <view class="cp-item"
                        :class="{'actived': index === currentIndex}"
                        v-for="(item, index) in plOrderPlResultResponses"
                        :key="index"
                        @click="handleChangeCurrent(item, index)"
                    >
                        <text class="id">皮料ID {{ item.plId }}</text>
                        <text class="cut">裁片：{{ item.cpQuantity }}</text>
                    </view>
                </view>
                <view class="base-info" @click="hidePopupList">
                    <view class="row-item">
                        <text>放置裁片数量</text>
                        <text>{{ currentCpItem.placedPartsCount }}</text>
                    </view>
                    <view class="row-item">
                        <text>已放置裁片面积 平方英尺</text>
                        <text>{{ currentCpItem.placedCpArea }}</text>
                    </view>
                    <view class="row-item">
                        <text>皮料面积 平方英尺</text>
                        <text>{{ currentCpItem.plArea }}</text>
                    </view>
                    <!--                    <view class="row-item">-->
                    <!--                        <text>供应商面积 平方英尺</text>-->
                    <!--                        <text>{{ currentCpItem.supplierArea }}</text>-->
                    <!--                    </view>-->
                    <view class="row-item">
                        <text>可重用废料面积 平方英尺</text>
                        <text>{{ currentCpItem.reusableArea }}</text>
                    </view>
                    <view class="row-item">
                        <text>利用率（%）</text>
                        <text>{{ currentCpItem.utilizationRate }}</text>
                    </view>
                </view>
                <view class="svg-container" @click="hidePopupList">
                    <image class="svg-img"
                        :src="currentCpItem.fileUrl"
                    ></image>
                </view>
                <view class="preview-part">
                    <view class="top-info">
                        <text>预览</text>
                        <view class="switch-container">
                            <text>款式：</text>
                            <uni-data-select class="style-switch"
                                v-model="currentStyleModeIndex"
                                :localdata="styleListData"
                                @change="handleStyleModeChange"
                            ></uni-data-select>
                        </view>
                    </view>
                    <view class="img-content" v-if="styleListData.length">
                        <image class="preview-img"
                            :src="modelRenderPath"
                            mode="aspectFit"
                        ></image>
                        <view class="preview-btn" @click="handleClickPreview">
                            <image class="access-logo" width="30" height="30" src="/static/box.svg"></image>
                            <text class="access-text">进入3D预览</text>
                        </view>
                    </view>
                    <view v-else class="no-data">暂无数据</view>
                </view>
            </template>
            <view v-else class="no-data"> 暂无数据 </view>
        </view>
        <view class="pl-detail__footer" @click="hidePopupList">
            <text @click="handleDownload('pdf')">下载PDF</text>
            <text @click="handleDownload('dxf')">下载DXF</text>
        </view>
        <uni-popup ref="downloadPopup" type="bottom" background-color="#fff" @change="handlePopupChange">
            <view class="download-popup-content">
                <uni-icons class="close-popup"
                    type="close"
                    color="#fff"
                    size="26"
                    @click="closeEditPopup"
                ></uni-icons>
                <view class="tip" v-if="type === 'pdf'">
                    <text>下载提示</text>
                    <text>第一步：</text>
                    <text style="margin-bottom: 30px">点击上方"预览文档"后，在预览页点击右上角"..."按钮</text>

                    <text>第二步：</text>
                    <text>在弹出抽屉页里面，选择相应操作完成下载</text>
                    <image
                        :src="downLoadTipImg"
                        mode="widthFix"
                    />
                </view>
                <view v-else class="tip">请复制文档地址，在浏览器地址栏输入地址即可下载</view>
                <button
                    class="button"
                    @click="handlePreviewDownload"
                >
                    {{ type === 'dxf' ? '复制文档地址' : '预览文档' }}
                </button>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import { getOrderResultDetail } from 'api/order';
import downLoadImg from 'src/static/downloadTip.png';

export default {
    data() {
        return {
            orderInfo: {},
            cpList: [],
            currentIndex: 0,
            currentStyleModeIndex: 0,
            showPopupList: false,
            plOrderPlResultResponses: [],
            svgUrl: '',
            cpFilePreviewBase64: '',
            baseUrl: this.$http.baseURL,
            token: '',
            saveFilePath: '',
            downLoadTipImg: downLoadImg,
            type: '',
            downloadUrlBase: 'http://plmaster.qianxunzj.com/plmaster/oss/',
        };
    },
    computed: {
        currentCpItem() {
            return this.plOrderPlResultResponses[this.currentIndex] || {};
        },
        styleListData() {
            return (this.currentCpItem.styleModelList || []).map((a, index) => {
                return {
                    text: a.styleName,
                    value: index,
                    dxfUrl: a.dxfUrl,
                    pdfUrl: a.pdfUrl,
                    modelUrl: a.modelUrl,
                    modelName: a.modelName,
                };
            });
        },
        modelRenderPath() {
            return `${this.$http.baseURL}/${this.styleListData[this.currentStyleModeIndex]?.modelUrl}?token=${this.token}`;
        },
    },
    onLoad(query) {
        this.orderId = query.id;
    },
    onShow() {
        this.svgUrl = '';
        this.cpFilePreviewBase64 = '';
        this.getData();
        this.token = uni.getStorageSync('accessToken');
    },
    methods: {
        handleClickPreview() {
            const contractUrl =
                encodeURIComponent(`https://plmaster.qianxunzj.com/3dview/index.html?model=${this.styleListData[this.currentStyleModeIndex]?.modelName}`);
            uni.navigateTo({
                url: `/pages/webviewRedirect/index?url=${contractUrl}`,
            });
        },
        closeEditPopup() {
            this.$refs.downloadPopup.close();
        },
        handlePopupChange(e) {
            if (!e.show) {
                this.currentEditIndex = '';
            }
        },
        handlePreviewDownload() {
            const token = uni.getStorageSync('accessToken');
            if (this.type === 'dxf') {
                uni.setClipboardData({
                    data: this.currentCpItem.dxfUrl + '?token=' +
                        token,
                    success() {
                        uni.showToast({
                            title: '复制成功',
                            icon: 'none',
                        });
                    },
                    fail(err) {
                        console.log('pay fail:' + JSON.stringify(err));
                    },
                });
            } else {
                uni.openDocument({
                    fileType: 'pdf',
                    filePath: this.saveFilePath,
                    showMenu: true,
                }).catch(e => {
                    console.log('e = ', e);
                });
            }
        },
        handleStyleModeChange(e) {
            console.log('handleStyleModeChange = ', e);
            this.renderImgSvg(this.modelRenderPath, 'svgUrl');
        },
        handleDownload(type) {
            this.type = type;
            if (type === 'dxf') {
                this.$refs.downloadPopup.open();
            } else {
                this.downloadFile(type);
            }
        },
        // 下载文件
        downloadFile() {
            const token = uni.getStorageSync('accessToken');
            uni.downloadFile({
                url:
                    `${this.currentCpItem.pdfUrl}?token=${token}`,
                header: {
                    'Authorization': token,
                },
                success: (result) => {
                    if (result.statusCode === 200) {
                        this.savePdfFile(result);
                    } else {
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none',
                        });
                    }
                },
                fail: () => {
                    uni.showToast({
                        title: '下载失败',
                        icon: 'none',
                    });
                },
            });
            // downloadTask.onProgressUpdate((res) => {
            //     this.percent = res.progress;
            // });
        },
        // 保存文件
        savePdfFile(file) {
            const fileNameStr = 'test';
            const saveFilePath = `${wx.env.USER_DATA_PATH}/${fileNameStr}.pdf`;
            wx.getFileSystemManager().saveFile({
                tempFilePath: file.tempFilePath,
                filePath: saveFilePath,
                success: (res) => {
                    this.saveFilePath = saveFilePath;
                    console.log('saveFilePath = ', saveFilePath, file.tempFilePath, res);
                    this.$refs.downloadPopup.open();
                },
                fail: ()  => {
                    uni.showToast({
                        title: '下载失败',
                        icon: 'none',
                    });
                },
            });
        },
        handleChangeCurrent(item, index) {
            this.currentIndex = index;
        },
        switchPopupListVisible() {
            this.showPopupList = !this.showPopupList;
        },
        hidePopupList() {
            this.showPopupList = false;
        },
        renderImgSvg(url, key) {
            const that = this;
            wx.request({
                url, // 你的接口地址
                method: 'GET', // 请求方式，默认为GET
                header: {
                    'content-type': 'application/json', // 默认值
                },
                success: function(res) {
                    // 请求成功的回调函数
                    // console.log('数据获取成功:', res.data, key);
                    const base64 = wx.arrayBufferToBase64(
                        new Uint8Array(unescape(encodeURIComponent(res.data)).split('').map(c => c.charCodeAt(0))),
                    );
                    const dataURL = `data:image/svg+xml;base64,${base64}`;
                    that[key] = dataURL;
                    // that.svgUrl = res.data.replace(/<svg/, `<svg width="${80}" height="${80}"`);
                },
                fail: function(err) {
                    // 请求失败的回调函数
                    console.error('数据获取失败:', err);
                },
            });
        },
        getData() {
            this.loading = true;
            uni.showLoading({
                title: '加载中...',
                mask: true,
            });
            getOrderResultDetail(this.orderId).then(res => {
                const { code, data } = res.data;
                if (code === 0) {
                    this.orderInfo = data;
                    this.plOrderPlResultResponses = data.plOrderPlResultResponses;
                    this.currentIndex = 0;
                    // this.renderImgSvg(this.modelRenderPath, 'svgUrl');
                    // this.renderImgSvg(this.baseUrl + '/plmaster/oss/' + this.currentCpItem.fileUrl + '?token=' + this.token,
                    //                   'cpFilePreviewBase64');
                }
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
    },
};
</script>

<style lang="scss">
.pl-detail {
    height: 100vh;
    background: #fff;
    overflow: hidden;
    &__header {
        padding: 16rpx;
        background: #000;
        position: fixed;
        width: 100vw;
        top: 0;
        z-index: 99;
        height: 100rpx;
        box-sizing: border-box;
        .row-item {
            height: 38rpx;
            display: flex;
            justify-content: space-between;
            .info-item {
                height: 36rpx;
                line-height: 36rpx;
                font-size: 28rpx;
                padding: 0 20rpx;
                .title {
                    color: #fff;
                    font-weight: bold;
                }
                .info {
                    color: #A7A7A7;
                }
                &:last-child {
                    width: 250rpx;
                }
            }
        }
    }
    &__content {
        height: calc(100% - 200rpx);
        background: #fff;
        position: relative;
        margin: 100rpx 0;
        z-index: 1;
        overflow: auto;
        .no-data {
            height: 180rpx;
            line-height: 180rpx;
            text-align: center;
            color: #666;
        }
        .order-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60rpx;
            font-size: 32rpx;
            padding: 0 20rpx;
            border-bottom: 1px solid #000;
            font-weight: 700;
            .id {
                flex: 1;
            }
            .cut {
                width: 150rpx;
            }
            .pl-select {
                width: 100rpx;
                margin-top: 10rpx;
                text-align: right;
            }
        }
        .popup-list {
            position: absolute;
            right: 0;
            width: 100vw;
            border: 1px solid #000;
            border-top: none;
            background: #fff;
            box-sizing: border-box;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            max-height: 800rpx;
            .cp-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 60rpx;
                font-size: 32rpx;
                padding: 0 20rpx;
                border-bottom: 1px solid #000;
                .id {
                    flex: 1;
                }
                .cut {
                    width: 250rpx;
                }
                &:last-child {
                    border-bottom: none;
                }
                &.actived {
                    background: #b2cff5;
                }
            }
        }
        .base-info {
            .row-item {
                display: flex;
                height: 54rpx;
                line-height: 54rpx;
                border-bottom: 1px solid #000;
                font-size: 28rpx;
                text {
                    width: 48%;
                }
                text:first-child {
                    text-align: right;
                    color: #1A1A1A;
                }
                text:last-child {
                    text-align: left;
                    padding-left: 60rpx;
                }

            }
        }
        .svg-container {
            margin-top: 40rpx;
            padding: 0 40rpx;
            .svg-img {
                //border-top: 1px solid #999;
                //background: #000;
                width: 100%;
            }
        }
        .preview-part {
            background: #F4F4F4;
            min-height: 500rpx;
            padding: 20rpx 40rpx;
            .top-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .switch-container {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .style-switch {
                    display: inline-block;
                    width: 300rpx;
                }
            }
            .img-content {
                margin-top: 20rpx;
                border-radius: 8rpx;
                min-height: 300rpx;
                position: relative;
                background: #d9d9d9;
                .preview-img {
                    width: 100%;
                }
                .preview-btn {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .access-logo {
                        width: 60rpx;
                        height: 60rpx;
                    }
                    .access-text {
                        display: inline-block;
                        color: #fff;
                        font-weight: 700;
                        font-size: 36rpx;
                    }
                }
            }
        }
    }
    &__footer {
        position: fixed;
        bottom: 0;
        height: 100rpx;
        width: 100vw;
        z-index: 99;
        background: #000;
        display: flex;
        align-items: center;
        color: #fff;
        font-weight: bold;
        text {
            width: 50%;
            text-align: center;
            font-size: 36rpx;
            &:first-child {
                border-right: 2px solid #fff;
            }
        }
    }
}
.download-popup-content {
    padding: 30rpx 40rpx;
    .button{
        height: 40px;
        background-color: #127FD2;
        border-radius: 4px;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        width: 200px;
        color: #FFFFFF;
        margin-top: 30px;
        line-height: 40px;
    }
    .tip{
        flex-direction: column;
        display: flex;
        text-align: left;
        margin-top: 10px;
        margin-bottom: 10px;
        text{
            font-size: 14px;
            color: #666666;
        }
        image{
            width:100%;
            margin-top: 10px;
        }
    }
}
</style>
