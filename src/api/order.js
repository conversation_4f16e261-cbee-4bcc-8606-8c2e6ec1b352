import Vue from 'vue';

/**
 * @description: 排料保存任务
 */
export function saveTask(data) {
    return Vue.$http.post('/plmaster/app/pl_order', data);
}

/**
 * @description: 排料修改任务
 */
export function configTask(data) {
    return Vue.$http.put('/plmaster/app/pl_order', data);
}

/**
 * @description: 排料任务详情
 */
export function getOrderBaseInfo(id) {
    return Vue.$http.get(`/plmaster/app/pl_order/${id}`);
}

export function getOrderResultDetail(id) {
    return Vue.$http.get(`/plmaster/app/pl_order/result/${id}`);
}

/**
 * @description: 裁片列表
 */
export function getCpList({ orderId }) {
    return Vue.$http.get('/plmaster/app/cp_order/list', { orderId });
}

/**
 * @description: 皮料列表
 */
export function getLetherList({ orderId }) {
    return Vue.$http.get('/plmaster/app/pl_order_leather_material/list', { orderId });
}

/**
 * @description: 筛选皮料列表
 */
export function getBatchLetherList(query) {
    return Vue.$http.get('/plmaster/app/pl_batch/page', query);
}
/**
 * @description: 筛选皮料列表
 */
export function getBatchLeatherSelectedList(query) {
    return Vue.$http.get('/plmaster/app/pl_batch_leather_material/list', query);
}

/**
 * @description: 筛选款式列表
 */
export function getStyleList(query) {
    return Vue.$http.get('/plmaster/app/cp_order/cp_style/page', query);
}

/**
 * @description: 任务已选裁片列表
 */
export function getOrderStyleList(cpId) {
    return Vue.$http.get(`/plmaster/app/cp_order/${cpId}`);
}
/**
 * @description: 获取任务原始裁片数量数据
 */
export function getOrderCpOriginDetail(orderId, cpId) {
    return Vue.$http.get(`/plmaster/cp_order/reset?orderId=${orderId}&cpId=${cpId}`);
}

/**
 * @description: 款式选择裁片列表
 */
export function getStyleCpList(query) {
    return Vue.$http.get('/plmaster/app/cp_order/cp_block/page', query);
}

/**
 * @description: 款式选择裁片列表
 */
export function saveStyleConfig(data) {
    return Vue.$http.post('/plmaster/app/cp_order/save_cp_order', data);
}

/**
 * @description: 款式选择裁片保存
 */
export function saveSelectedCpData(data) {
    return Vue.$http.post('/plmaster/app/cp_order/add_block', data);
}

/**
 * @description: 选择皮料保存
 */
export function saveLeatherSelectedData(data) {
    return Vue.$http.post('/plmaster/app/pl_order_leather_material/confirm', data);
}

/**
 * @description: 修改皮料顺序
 */
export function orderLeatherSelectedData(data) {
    return Vue.$http.post('/plmaster/app/pl_order_leather_material', data);
}

/**
 * @description: 获取皮料预览svg信息
 */
export function getLeatherPreviewInfo(id) {
    return Vue.$http.get(`/plmaster/app/pl_batch_leather_material/${id}`);
}

/**
 * @description: 获取款式预览svg信息
 */
export function getStylePreviewInfo(id) {
    return Vue.$http.get(`/plmaster/cp_style/${id}`);
}

/**
 * @description: 发布订单
 */
export function releaseOrder(data) {
    return Vue.$http.post(`/plmaster/app/pl_order/release`, data);
}

/**
 * @description: 删除皮料
 */
export function deleteLeather(id) {
    return Vue.$http.delete(`/plmaster/app/pl_order_leather_material/${id}`);
}

/**
 * @description: 删除已选款式
 */
export function deleteOrderStyle(data) {
    return Vue.$http.delete(`/plmaster/app/cp_order/delete_cp_style`, data);
}

// 新增：终止/取消任务操作
export function operateOrder(data) {
    // data: { id, action }  action: 2=终止, 1=取消
    return Vue.$http.post('/plmaster/pl_order/operate', data);
}

// 新增：删除任务，参数为id数组
export function deleteOrder(id) {
    // ids: [id]
    return Vue.$http.post(`/plmaster/app/pl_order/delete/${id}`);
}

// 新增：皮料回库
export function returnWarehouse(id) {
    return Vue.$http.post(`/plmaster/pl_order/return_warehouse/${id}`);
}

// 新增：复制任务
export function copyOrder(id) {
    return Vue.$http.post(`/plmaster/pl_order/copy/${id}`);
}
