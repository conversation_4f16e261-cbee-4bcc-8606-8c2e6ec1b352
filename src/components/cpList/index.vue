<template>
    <view class="cp-list">
        <view class="common-table">
            <view class="common-table__header">
                <view class="th-item order">编号</view>
                <view class="th-item flex">已选择的款式</view>
                <view class="th-item area">总面积(ft²)</view>
            </view>
            <view class="common-table__body">

                <uni-swipe-action class="swipe-row" v-for="(item, index) in tableData" :key="index">
                    <uni-swipe-action-item
                        class="swipe-item"
                        @click="optionClick(item, $event)"
                        :right-options="isInPopup ? [] : rightOptions"
                    >
                        <view class="row-item"
                            @click="handleToDetail(item)"
                            :key="index"
                            :class="{'light-bg':
                                index%2, 'active': (Number(activeId) === item.id && isInPopup)}"
                        >
                            <view class="row-item__column order">{{ index + 1 }}</view>
                            <view class="row-item__column flex">
                                <view class="info-name">{{ item.styleName }}</view>
                                <view class="info-detail">
                                    <text class="info-detail-item">
                                        <text class="title">ID：</text>
                                        <text class="num">{{ item.id }}</text>
                                    </text>
                                    <text class="info-detail-item">
                                        <text class="title">裁片数：</text>
                                        <text class="num">{{ item.cpQuantity || 0 }}</text>
                                    </text>
                                    <text class="info-detail-item" v-if="isInPopup">
                                        <text class="title">优先级：</text>
                                        <text class="num">{{ item.cpPriority || 0 }}</text>
                                    </text>
                                </view>
                            </view>
                            <view class="row-item__column area">{{ renderArea(item) }}</view>
                        </view>
                    </uni-swipe-action-item>
                </uni-swipe-action>
                <view class="no-data" v-if="!tableData.length">
                    暂无数据
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        activeId: {
            type: String,
            default: '',
        },
        orderId: {
            type: String,
            default: '',
        },
        tableData: {
            type: Array,
            default: () => ([]),
        },
        isInPopup: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            rightOptions: [{
                text: '删除',
                type: 'delete',
                style: {
                    backgroundColor: '#FF0000',
                },
            }],
        };
    },
    computed: {
        renderArea() {
            return (item) => {
                const area = item.totalArea || item.area;
                return area ? area.toFixed(2) : 0;
            };
        },
    },
    onLoad() {

    },
    methods: {
        optionClick(item) {

        },
        handleToDetail(item) {
            if (this.isInPopup) {
                this.$emit('rowClick', item.id);
                return;
            }
            const readonlyParam = this.readonly ? '&readonly=true' : '';
            uni.navigateTo({
                url: `/pages/styleDetail/index?orderId=${this.orderId}&id=${item.id}${readonlyParam}`,
            });
        },
    },
};
</script>

<style lang="scss">
.cp-list {
    height: calc(100% - 145rpx);
    .common-table {
        height: 100%;
        overflow-y: auto;
        box-sizing: border-box;
        .row-item.active {
            background: #b5dcfb;
        }
    }
}
</style>
